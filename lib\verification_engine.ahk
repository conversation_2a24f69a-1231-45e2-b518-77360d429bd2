;; === VERIFICATION ENGINE ===
; Centralized data-driven verification system
; All verification logic is handled through database state, not GUI state
; GUI controls reflect database state but never drive verification decisions

class VerificationEngine {

    ;; === MAIN VERIFICATION STATUS CHECK ===
    ; Called after any verification action to update overall status
    ; @param rollNumber: The candidate's roll number
    ; @return: True if all required verifications are complete, false otherwise
    static CheckVerificationStatus(rollNumber) {
        try {
            global g_isPostExamMode, g_isVerifyMode

            ; Load fresh candidate data from database
            candidateData := LoadCandidateData(rollNumber)
            if (!candidateData || candidateData.Name == "") {
                ErrorHandler.LogMessage("ERROR", "VerificationEngine: Invalid candidate data for " rollNumber)
                return false
            }

            ; Route to appropriate mode-specific verification logic
            if (g_isPostExamMode) {
                return this.CheckPostExamVerification(rollNumber, candidateData)
            } else if (g_isVerifyMode) {
                return this.CheckVerifyModeVerification(rollNumber, candidateData)
            } else {
                return this.CheckPreExamVerification(rollNumber, candidateData)
            }
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "VerificationEngine.CheckVerificationStatus error: " err.Message)
            return false
        }
    }

    ;; === PRE-EXAM VERIFICATION LOGIC ===
    ; Uses ThumbPreference-based logic for determining completion
    static CheckPreExamVerification(rollNumber, candidateData) {
        try {
            global SignatureVerificationEnabled

            ; Check photo verification (always required)
            photoVerified := (candidateData.PhotoStatus == "Verified")

            ; Check signature verification (if enabled)
            signatureVerified := (!SignatureVerificationEnabled || candidateData.SignatureStatus == "Verified")

            ; Check thumbprint verification based on ThumbPreference
            thumbprintVerified := this.CheckThumbprintVerification(candidateData)

            ; Determine overall verification status
            allVerified := (photoVerified && thumbprintVerified && signatureVerified)

            ; Update BiometricStatus in database
            candidatesPath := PathManager.GetDatabaseFilePath("Candidates")
            if (allVerified) {
                IniWrite("Verified", candidatesPath, rollNumber, "BiometricStatus")
                ErrorHandler.LogMessage("INFO", "Pre-exam BiometricStatus set to Verified for candidate: " rollNumber)
            } else {
                ; Clear BiometricStatus if not all requirements are met
                currentStatus := IniRead(candidatesPath, rollNumber, "BiometricStatus", "")
                if (currentStatus == "Verified") {
                    IniWrite("", candidatesPath, rollNumber, "BiometricStatus")
                    ErrorHandler.LogMessage("INFO", "Pre-exam BiometricStatus cleared (was Verified) for candidate: " rollNumber)
                }
            }

            ; Update GUI to reflect database state
            this.UpdatePreExamGUI(rollNumber, candidateData, allVerified)

            ErrorHandler.LogMessage("DEBUG", "Pre-exam verification check: Photo=" photoVerified ", Thumbs=" thumbprintVerified ", Signature=" signatureVerified ", Overall=" allVerified)

            return allVerified
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "VerificationEngine.CheckPreExamVerification error: " err.Message)
            return false
        }
    }

    ;; === POST-EXAM VERIFICATION LOGIC ===
    ; Uses existing post-exam verification utilities
    static CheckPostExamVerification(rollNumber, candidateData) {
        try {
            ; Delegate to existing post-exam verification function
            return CheckPostExamVerifications(rollNumber)
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "VerificationEngine.CheckPostExamVerification error: " err.Message)
            return false
        }
    }

    ;; === VERIFY MODE VERIFICATION LOGIC ===
    ; Simple verify mode verification (fallback implementation)
    static CheckVerifyModeVerification(rollNumber, candidateData) {
        try {
            ; Simple verify mode logic - just check if all basic verifications are complete
            ; This is a fallback implementation since VerifyModeManager has syntax issues
            global SignatureVerificationEnabled

            ; Check photo verification
            photoVerified := (candidateData.VerifyPhotoStatus == "Verified")

            ; Check signature verification (if enabled)
            signatureVerified := (!SignatureVerificationEnabled || candidateData.VerifySignatureStatus == "Verified")

            ; Check fingerprint verification
            fingerprintVerified := (candidateData.VerifyFingerprintStatus == "Verified")

            ; Determine overall verification status
            allVerified := (photoVerified && fingerprintVerified && signatureVerified)

            ; Update VerifyBiometricStatus in database
            candidatesPath := PathManager.GetDatabaseFilePath("Candidates")
            if (allVerified) {
                IniWrite("Verified", candidatesPath, rollNumber, "VerifyBiometricStatus")
                ErrorHandler.LogMessage("INFO", "Verify mode BiometricStatus set to Verified for candidate: " rollNumber)
            } else {
                IniWrite("Incomplete", candidatesPath, rollNumber, "VerifyBiometricStatus")
                ErrorHandler.LogMessage("DEBUG", "Verify mode BiometricStatus set to Incomplete for candidate: " rollNumber)
            }

            return allVerified
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "VerificationEngine.CheckVerifyModeVerification error: " err.Message)
            return false
        }
    }

    ;; === THUMBPRINT VERIFICATION LOGIC ===
    ; Centralized logic for checking thumbprint verification based on ThumbPreference
    static CheckThumbprintVerification(candidateData) {
        try {
            ; Get ThumbPreference setting
            thumbPreference := candidateData.ThumbPreference
            if (thumbPreference == "") {
                thumbPreference := "Both"  ; Default to Both if not set
            }

            ; Check individual thumb statuses (accept both "Saved" and "Verified")
            leftThumbVerified := (candidateData.FingerprintStatus == "Saved" || candidateData.FingerprintStatus == "Verified")
            rightThumbVerified := (candidateData.RightFingerprintStatus == "Saved" || candidateData.RightFingerprintStatus == "Verified")

            ; Apply ThumbPreference logic
            thumbprintVerified := false
            if (thumbPreference == "Both") {
                ; Both thumbs must be verified
                thumbprintVerified := (leftThumbVerified && rightThumbVerified)
                ErrorHandler.LogMessage("DEBUG", "ThumbPreference=Both: left=" leftThumbVerified ", right=" rightThumbVerified ", result=" thumbprintVerified)
            } else if (thumbPreference == "Left") {
                ; Only left thumb must be verified
                thumbprintVerified := leftThumbVerified
                ErrorHandler.LogMessage("DEBUG", "ThumbPreference=Left: left=" leftThumbVerified ", result=" thumbprintVerified)
            } else if (thumbPreference == "Right") {
                ; Only right thumb must be verified
                thumbprintVerified := rightThumbVerified
                ErrorHandler.LogMessage("DEBUG", "ThumbPreference=Right: right=" rightThumbVerified ", result=" thumbprintVerified)
            } else {
                ; Unknown preference, default to Both
                thumbprintVerified := (leftThumbVerified && rightThumbVerified)
                ErrorHandler.LogMessage("DEBUG", "ThumbPreference=" thumbPreference " (unknown), defaulting to Both, result=" thumbprintVerified)
            }

            return thumbprintVerified
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "VerificationEngine.CheckThumbprintVerification error: " err.Message)
            return false
        }
    }

    ;; === GUI UPDATE FUNCTIONS ===
    ; Update GUI controls to reflect database state for pre-exam mode
    static UpdatePreExamGUI(rollNumber, candidateData, allVerified) {
        try {
            global VerificationStatusValue, ButtonAssignSeat

            ; Update verification status display
            if (allVerified) {
                VerificationStatusValue.Text := "Verified"
                VerificationStatusValue.Opt("cGreen")
                ButtonAssignSeat.Enabled := true
                ButtonAssignSeat.Visible := true
                ErrorHandler.LogMessage("DEBUG", "Pre-exam GUI updated: Verification complete, seat assignment enabled")
            } else {
                VerificationStatusValue.Text := "Incomplete"
                VerificationStatusValue.Opt("cRed")
                ButtonAssignSeat.Enabled := false
                ErrorHandler.LogMessage("DEBUG", "Pre-exam GUI updated: Verification incomplete, seat assignment disabled")
            }
        } catch as err {
            ErrorHandler.LogMessage("WARNING", "Error updating pre-exam GUI: " err.Message)
        }
    }

    ;; === FINGERPRINT SAVE HANDLER ===
    ; Called when fingerprint is successfully saved to update verification status
    static OnFingerprintSaved(rollNumber, isRightThumb := false) {
        try {
            ; Update the appropriate fingerprint status to "Saved"
            candidatesPath := PathManager.GetDatabaseFilePath("Candidates")
            global g_isPostExamMode, g_isVerifyMode

            ; Only update status for pre-exam mode (post-exam and verify modes handle their own status)
            if (!g_isPostExamMode && !g_isVerifyMode) {
                if (isRightThumb) {
                    IniWrite("Saved", candidatesPath, rollNumber, "RightFingerprintStatus")
                    ErrorHandler.LogMessage("INFO", "Right fingerprint saved for candidate: " rollNumber)
                } else {
                    IniWrite("Saved", candidatesPath, rollNumber, "FingerprintStatus")
                    ErrorHandler.LogMessage("INFO", "Left fingerprint saved for candidate: " rollNumber)
                }

                ; Immediately check verification status
                this.CheckVerificationStatus(rollNumber)
            }

        } catch as err {
            ErrorHandler.LogMessage("ERROR", "VerificationEngine.OnFingerprintSaved error: " err.Message)
        }
    }

    ;; === PHOTO VERIFICATION HANDLER ===
    ; Called when photo is successfully verified
    static OnPhotoVerified(rollNumber) {
        try {
            ; Update photo status to "Verified"
            candidatesPath := PathManager.GetDatabaseFilePath("Candidates")
            global g_isPostExamMode, g_isVerifyMode

            if (g_isPostExamMode) {
                IniWrite("Verified", candidatesPath, rollNumber, "PostExamPhotoStatus")
                ErrorHandler.LogMessage("INFO", "Post-exam photo verified for candidate: " rollNumber)
            } else if (g_isVerifyMode) {
                IniWrite("Verified", candidatesPath, rollNumber, "VerifyPhotoStatus")
                ErrorHandler.LogMessage("INFO", "Verify mode photo verified for candidate: " rollNumber)
            } else {
                IniWrite("Verified", candidatesPath, rollNumber, "PhotoStatus")
                ErrorHandler.LogMessage("INFO", "Pre-exam photo verified for candidate: " rollNumber)
            }

            ; Immediately check verification status
            this.CheckVerificationStatus(rollNumber)

        } catch as err {
            ErrorHandler.LogMessage("ERROR", "VerificationEngine.OnPhotoVerified error: " err.Message)
        }
    }

    ;; === SIGNATURE VERIFICATION HANDLER ===
    ; Called when signature is successfully verified
    static OnSignatureVerified(rollNumber) {
        try {
            ; Update signature status to "Verified"
            candidatesPath := PathManager.GetDatabaseFilePath("Candidates")
            global g_isPostExamMode, g_isVerifyMode

            if (g_isPostExamMode) {
                IniWrite("Verified", candidatesPath, rollNumber, "PostExamSignatureStatus")
                ErrorHandler.LogMessage("INFO", "Post-exam signature verified for candidate: " rollNumber)
            } else if (g_isVerifyMode) {
                IniWrite("Verified", candidatesPath, rollNumber, "VerifySignatureStatus")
                ErrorHandler.LogMessage("INFO", "Verify mode signature verified for candidate: " rollNumber)
            } else {
                IniWrite("Verified", candidatesPath, rollNumber, "SignatureStatus")
                ErrorHandler.LogMessage("INFO", "Pre-exam signature verified for candidate: " rollNumber)
            }

            ; Immediately check verification status
            this.CheckVerificationStatus(rollNumber)

        } catch as err {
            ErrorHandler.LogMessage("ERROR", "VerificationEngine.OnSignatureVerified error: " err.Message)
        }
    }
}
